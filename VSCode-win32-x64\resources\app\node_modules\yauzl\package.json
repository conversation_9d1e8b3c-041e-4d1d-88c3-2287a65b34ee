{"name": "yauzl", "version": "3.1.1", "description": "yet another unzip library for node", "engines": {"node": ">=12"}, "main": "index.js", "scripts": {"test": "node test/test.js"}, "repository": {"type": "git", "url": "git+https://github.com/thejoshwolfe/yauzl.git"}, "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": "<PERSON> <the<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "homepage": "https://github.com/thejoshwolfe/yauzl", "dependencies": {"buffer-crc32": "~0.2.3", "pend": "~1.2.0"}, "devDependencies": {"bl": "^6.0.11"}, "files": ["fd-slicer.js", "index.js"]}